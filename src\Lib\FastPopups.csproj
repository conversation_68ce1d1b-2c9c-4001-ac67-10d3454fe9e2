﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-maccatalyst;net9.0-windows10.0.19041.0</TargetFrameworks>
    <OutputType>Library</OutputType>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>

    <Title>Fast Popups for .NET MAUI</Title>
    <PackageId>AppoMobi.Maui.FastPopups</PackageId>
    <Description>Popup controls for .NET MAUI derived from CommunityToolkit Popups</Description>
    <PackageTags>fastpopups popups maui popup modal dialog appomobi</PackageTags>
    <Version>*******</Version>
    <Authors>Nick Kovalsky aka AppoMobi and contributors</Authors>
    <RepositoryUrl>https://github.com/taublast/popups</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.70" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Platforms\iOS\" />
    <Folder Include="Platforms\MacCatalyst\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md" Link="README.md" />
  </ItemGroup>

</Project>
