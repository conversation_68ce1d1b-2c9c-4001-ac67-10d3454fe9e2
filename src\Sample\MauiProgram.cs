﻿global using FastPopups;
using Microsoft.Extensions.Logging;

namespace SampleApp;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			})
			.AddPopups(); // Register popup handlers

#if DEBUG
		builder.Logging.AddDebug();
#endif

		return builder.Build();
	}
}
